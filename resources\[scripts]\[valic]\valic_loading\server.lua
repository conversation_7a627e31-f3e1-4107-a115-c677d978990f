-- Server side script pro valic_loading

-- Event pro aktualizaci loading progress ze serveru
RegisterNetEvent('valic_loading:serverUpdateProgress')
AddEventHandler('valic_loading:serverUpdateProgress', function(progress, text)
    local source = source
    TriggerClientEvent('valic_loading:updateProgress', source, progress, text)
end)

-- Event pro zavření loading screenu ze serveru
RegisterNetEvent('valic_loading:serverCloseLoadingScreen')
AddEventHandler('valic_loading:serverCloseLoadingScreen', function()
    local source = source
    TriggerClientEvent('valic_loading:closeLoadingScreen', source)
end)

-- Funkce pro broadcast loading progress všem hráčům
function BroadcastLoadingProgress(progress, text)
    TriggerClientEvent('valic_loading:updateProgress', -1, progress, text)
end

-- Funkce pro zavření loading screenu všem hráčům
function BroadcastCloseLoadingScreen()
    TriggerClientEvent('valic_loading:closeLoadingScreen', -1)
end

-- Export funkce pro použití v jiných scriptech
exports('updateLoadingProgress', function(playerId, progress, text)
    TriggerClientEvent('valic_loading:updateProgress', playerId, progress, text)
end)

exports('closeLoadingScreen', function(playerId)
    TriggerClientEvent('valic_loading:closeLoadingScreen', playerId)
end)

exports('broadcastLoadingProgress', BroadcastLoadingProgress)
exports('broadcastCloseLoadingScreen', BroadcastCloseLoadingScreen)

-- Log při startu
print("^2[valic_loading] Server script loaded successfully^7")

-- Event při připojení hráče
AddEventHandler('playerConnecting', function(name, setKickReason, deferrals)
    local source = source
    print("^3[valic_loading] Player " .. name .. " is connecting (ID: " .. source .. ")^7")
end)

-- Event při odpojení hráče
AddEventHandler('playerDropped', function(reason)
    local source = source
    print("^1[valic_loading] Player disconnected (ID: " .. source .. ") - Reason: " .. reason .. "^7")
end)