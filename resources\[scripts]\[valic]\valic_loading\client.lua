local isLoadingScreenActive = true
local loadingProgress = 0

-- Funkce pro aktualizaci loading progress
local function updateLoadingProgress(progress, text)
    if isLoadingScreenActive then
        SendLoadingScreenMessage(json.encode({
            eventName = 'loadProgress',
            loadFraction = progress / 100,
            loadingText = text
        }))
    end
end

-- HTTP callback handler pro loading screen
RegisterNUICallback('loadingComplete', function(data, cb)
    if isLoadingScreenActive then
        print("^2[valic_loading] Loading screen fade out completed, shutting down^7")
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
    cb('ok')
end)

-- Simulace loading progress (protože GetLoadingScreenLoadFraction není dos<PERSON>)
Citizen.CreateThread(function()
    local simulatedProgress = 0
    local progressSteps = {
        {delay = 1000, progress = 5, text = "Inicializace..."},
        {delay = 2000, progress = 15, text = "Načítám mapu..."},
        {delay = 1500, progress = 25, text = "Mapa načtena..."},
        {delay = 2000, progress = 35, text = "Připojuji session..."},
        {delay = 1800, progress = 45, text = "Načítám prohlížeč..."},
        {delay = 1200, progress = 55, text = "Načítám detaily..."},
        {delay = 1500, progress = 65, text = "Dokončuji session..."},
        {delay = 2000, progress = 75, text = "Připravuji svět..."},
        {delay = 1800, progress = 85, text = "Svět připraven..."},
        {delay = 1000, progress = 95, text = "Dokončuji..."},
        {delay = 2000, progress = 100, text = "Vítejte v Diverse RP!"}
    }
    
    for _, step in ipairs(progressSteps) do
        if not isLoadingScreenActive then break end
        
        Citizen.Wait(step.delay)
        simulatedProgress = step.progress
        loadingProgress = simulatedProgress
        updateLoadingProgress(simulatedProgress, step.text)
        
        if simulatedProgress >= 100 then
            Citizen.Wait(2000) -- Počkej 2 sekundy před zavřením
            if isLoadingScreenActive then
                ShutdownLoadingScreen()
                isLoadingScreenActive = false
            end
            break
        end
    end
end)

-- Alternativní metoda pro detekci dokončení loadingu
Citizen.CreateThread(function()
    while isLoadingScreenActive do
        -- Kontroluj zda je hráč spawnutý
        if NetworkIsPlayerActive(PlayerId()) then
            Citizen.Wait(3000) -- Počkej 3 sekundy po spawnu
            if isLoadingScreenActive then
                updateLoadingProgress(100, "Připojení dokončeno!")
                Citizen.Wait(1500)
                ShutdownLoadingScreen()
                isLoadingScreenActive = false
            end
            break
        end
        Citizen.Wait(500)
    end
end)

-- Event handlery pro custom loading stavy
RegisterNetEvent('valic_loading:updateProgress')
AddEventHandler('valic_loading:updateProgress', function(progress, text)
    updateLoadingProgress(progress, text)
end)

RegisterNetEvent('valic_loading:closeLoadingScreen')
AddEventHandler('valic_loading:closeLoadingScreen', function()
    if isLoadingScreenActive then
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)

-- Automatické zavření loading screenu po připojení
AddEventHandler('playerSpawned', function()
    if isLoadingScreenActive then
        Citizen.Wait(1000)
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)

-- Backup timer pro případ, že se loading screen nezavře automaticky
Citizen.CreateThread(function()
    Citizen.Wait(60000) -- 60 sekund timeout
    if isLoadingScreenActive then
        print("^3[valic_loading] Loading screen timeout - force closing^7")
        ShutdownLoadingScreen()
        isLoadingScreenActive = false
    end
end)